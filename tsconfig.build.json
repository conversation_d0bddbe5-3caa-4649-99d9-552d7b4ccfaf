// tsconfig.build.json
{
    // 
    "extends": "./tsconfig.json",
    // "include": ["src/components"],
    "include": ["src", "shims-vue.d.ts"],
    // "exclude": ["docs", "node_modules", "dist", "src/components"],
    "exclude": ["docs", "node_modules", "dist"],
    "compilerOptions": {
        "declaration": true, // 生成 .d.ts 类型文件
        "emitDeclarationOnly": true,  // 只生成类型，不输出 JS
        "outDir": "dist/types",  // 类型文件输出目录
        "baseUrl": ".", // 支持路径别名
        "paths": {
            "@/*": ["src/*"],
            "@docs/*":["docs/*"],
            "@demos/*":["demos/*"],
            "@components/*":["components/*"],
        },
        "module": "ESNext",
        "target": "ESNext",  // 编译目标
        "moduleResolution": "Node",
        "jsx": "preserve",
        "esModuleInterop": true,
        "strict": true,
        "skipLibCheck": true
    }
}
