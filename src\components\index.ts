import type { App } from "vue"; // ✅ 这是类型，不是对象

import YfButton from "./yf-button";
import YfAlert from "./yf-alert";

const components = [YfButton, YfAlert];

const install = (app: App): void => {
    components.forEach((comp) => {
        console.log("comp111111111111111", comp);

        const name = comp.name || (comp as any).__name;
        if (!name) {
            console.warn("组件缺少 name 属性，无法注册：", comp);
            return;
        }
        app.component(name, comp);
        // app.component(comp.name || comp.__name, comp)
    });
};

export { YfButton, YfAlert };
export default { install };
