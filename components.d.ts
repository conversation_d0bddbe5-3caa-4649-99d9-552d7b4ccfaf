/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    HelloWorld: typeof import('./src/components/HelloWorld.vue')['default']
    YfAlert: typeof import('./src/components/yf-alert/yf-alert.vue')['default']
    YfButton: typeof import('./src/components/yf-button/yf-button.vue')['default']
  }
}
