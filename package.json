{"name": "my-ui-library", "private": true, "version": "1.0.0", "type": "module", "main": "dist/index.cjs.js", "module": "dist/index.es.js", "types": "dist/index.d.ts", "sideEffects": false, "exports": {".": {"import": "./dist/index.es.js", "require": "./dist/index.cjs.js", "types": "./dist/index.d.ts"}, "./style": {"import": "./dist/style/index.css"}}, "scripts": {"dev": "vite", "preview": "vite preview", "docs:dev": "vitepress dev docs", "docs:build": "vitepress build docs", "docs:preview": "vitepress preview docs", "clean": "<PERSON><PERSON><PERSON> dist", "build:types": "vue-tsc --project tsconfig.build.json", "build:es": "vite build --config vite.config.build.ts", "build:lib": "vite build --config vite.config.lib.ts", "build:dts": "vite build --config vite.config.dts.ts", "copy:types": "cpx \"dist/entry-types/index.d.ts\" dist/", "build": "npm run clean && npm run build:types && npm run build:es && npm run build:lib && npm run build:dts && npm run copy:types"}, "dependencies": {"vue": "^3.5.17"}, "devDependencies": {"@types/node": "^24.0.10", "@vitejs/plugin-vue": "^6.0.0", "@vue/tsconfig": "^0.7.0", "cpx": "^1.5.0", "rimraf": "^6.0.1", "rollup": "^4.44.2", "rollup-plugin-css-only": "^4.5.2", "rollup-plugin-dts": "^6.2.1", "sass": "^1.89.2", "typescript": "~5.8.3", "unplugin-vue-components": "^28.8.0", "vite": "^7.0.3", "vitepress": "^1.6.3", "vue-tsc": "^2.2.10"}}